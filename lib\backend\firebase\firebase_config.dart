import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';

Future initFirebase() async {
  if (kIsWeb) {
    await Firebase.initializeApp(
        options: FirebaseOptions(
            apiKey: "AIzaSyAToMhqIhhgygNb2eQDU0zJCW8CgtbyCf8",
            authDomain: "journeyman-jobs.firebaseapp.com",
            projectId: "journeyman-jobs",
            storageBucket: "journeyman-jobs.firebasestorage.app",
            messagingSenderId: "1037879032120",
            appId: "1:1037879032120:web:5b32850ec32c534d2fe9cd",
            measurementId: "G-NXHJR8P8KT"));
  } else {
    await Firebase.initializeApp();
  }
}
