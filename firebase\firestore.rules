rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /jobs/{document} {
      allow create: if false;
      allow read: if true;
      allow write: if false;
      allow delete: if false;
    }

    match /locals/{document} {
      allow create: if false;
      allow read: if true;
      allow write: if false;
      allow delete: if false;
    }

    match /users/{document} {
      allow create: if request.auth != null;
      allow read: if true;
      allow write: if request.auth != null;
      allow delete: if false;
    }
  }
}
