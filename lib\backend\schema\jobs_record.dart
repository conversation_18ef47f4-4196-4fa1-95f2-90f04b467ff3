import 'dart:async';

import 'package:collection/collection.dart';

import '/backend/schema/util/firestore_util.dart';

import 'index.dart';
import '/flutter_flow/flutter_flow_util.dart';

class JobsRecord extends FirestoreRecord {
  JobsRecord._(
    DocumentReference reference,
    Map<String, dynamic> data,
  ) : super(reference, data) {
    _initializeFields();
  }

  // "local" field.
  int? _local;
  int get local => _local ?? 0;
  bool hasLocal() => _local != null;

  // "classification" field.
  String? _classification;
  String get classification => _classification ?? '';
  bool hasClassification() => _classification != null;

  // "company" field.
  String? _company;
  String get company => _company ?? '';
  bool hasCompany() => _company != null;

  // "location" field.
  String? _location;
  String get location => _location ?? '';
  bool hasLocation() => _location != null;

  // "hours" field.
  String? _hours;
  String get hours => _hours ?? '';
  bool hasHours() => _hours != null;

  // "wage" field.
  String? _wage;
  String get wage => _wage ?? '';
  bool hasWage() => _wage != null;

  // "sub" field.
  String? _sub;
  String get sub => _sub ?? '';
  bool hasSub() => _sub != null;

  // "jobClass" field.
  String? _jobClass;
  String get jobClass => _jobClass ?? '';
  bool hasJobClass() => _jobClass != null;

  // "localNumber" field.
  int? _localNumber;
  int get localNumber => _localNumber ?? 0;
  bool hasLocalNumber() => _localNumber != null;

  // "qualifications" field.
  String? _qualifications;
  String get qualifications => _qualifications ?? '';
  bool hasQualifications() => _qualifications != null;

  // "date_posted" field.
  String? _datePosted;
  String get datePosted => _datePosted ?? '';
  bool hasDatePosted() => _datePosted != null;

  // "job_description" field.
  String? _jobDescription;
  String get jobDescription => _jobDescription ?? '';
  bool hasJobDescription() => _jobDescription != null;

  // "job_title" field.
  String? _jobTitle;
  String get jobTitle => _jobTitle ?? '';
  bool hasJobTitle() => _jobTitle != null;

  // "per_diem" field.
  String? _perDiem;
  String get perDiem => _perDiem ?? '';
  bool hasPerDiem() => _perDiem != null;

  // "agreement" field.
  String? _agreement;
  String get agreement => _agreement ?? '';
  bool hasAgreement() => _agreement != null;

  // "numberOfJobs" field.
  String? _numberOfJobs;
  String get numberOfJobs => _numberOfJobs ?? '';
  bool hasNumberOfJobs() => _numberOfJobs != null;

  // "timestamp" field.
  DateTime? _timestamp;
  DateTime? get timestamp => _timestamp;
  bool hasTimestamp() => _timestamp != null;

  // "startDate" field.
  String? _startDate;
  String get startDate => _startDate ?? '';
  bool hasStartDate() => _startDate != null;

  // "startTime" field.
  String? _startTime;
  String get startTime => _startTime ?? '';
  bool hasStartTime() => _startTime != null;

  // "booksYourOn" field.
  List<int>? _booksYourOn;
  List<int> get booksYourOn => _booksYourOn ?? const [];
  bool hasBooksYourOn() => _booksYourOn != null;

  void _initializeFields() {
    _local = _safeParseInt(snapshotData['local']);
    _classification = snapshotData['classification'] as String?;
    _company = snapshotData['company'] as String?;
    _location = snapshotData['location'] as String?;
    _hours = snapshotData['hours'] as String?;
    _wage = snapshotData['wage'] as String?;
    _sub = snapshotData['sub'] as String?;
    _jobClass = snapshotData['jobClass'] as String?;
    _localNumber = _safeParseInt(snapshotData['localNumber']);
    _qualifications = snapshotData['qualifications'] as String?;
    _datePosted = snapshotData['date_posted'] as String?;
    _jobDescription = snapshotData['job_description'] as String?;
    _jobTitle = snapshotData['job_title'] as String?;
    _perDiem = snapshotData['per_diem'] as String?;
    _agreement = snapshotData['agreement'] as String?;
    _numberOfJobs = snapshotData['numberOfJobs'] as String?;
    _timestamp = snapshotData['timestamp'] as DateTime?;
    _startDate = snapshotData['startDate'] as String?;
    _startTime = snapshotData['startTime'] as String?;
    _booksYourOn = _safeParseIntList(snapshotData['booksYourOn']);
  }

  // Helper method to safely parse integers from various data types
  int? _safeParseInt(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) {
      try {
        return int.parse(value);
      } catch (e) {
        print('Warning: Could not parse "$value" as int, defaulting to null');
        return null;
      }
    }
    return null;
  }

  // Helper method to safely parse list of integers
  List<int> _safeParseIntList(dynamic value) {
    if (value == null) return [];
    if (value is List) {
      return value.map((item) => _safeParseInt(item) ?? 0).toList();
    }
    return [];
  }

  static CollectionReference get collection =>
      FirebaseFirestore.instance.collection('jobs');

  static Stream<JobsRecord> getDocument(DocumentReference ref) =>
      ref.snapshots().map((s) => JobsRecord.fromSnapshot(s));

  static Future<JobsRecord> getDocumentOnce(DocumentReference ref) =>
      ref.get().then((s) => JobsRecord.fromSnapshot(s));

  static JobsRecord fromSnapshot(DocumentSnapshot snapshot) => JobsRecord._(
        snapshot.reference,
        mapFromFirestore(snapshot.data() as Map<String, dynamic>),
      );

  static JobsRecord getDocumentFromData(
    Map<String, dynamic> data,
    DocumentReference reference,
  ) =>
      JobsRecord._(reference, mapFromFirestore(data));

  @override
  String toString() =>
      'JobsRecord(reference: ${reference.path}, data: $snapshotData)';

  @override
  int get hashCode => reference.path.hashCode;

  @override
  bool operator ==(other) =>
      other is JobsRecord &&
      reference.path.hashCode == other.reference.path.hashCode;
}

Map<String, dynamic> createJobsRecordData({
  int? local,
  String? classification,
  String? company,
  String? location,
  String? hours,
  String? wage,
  String? sub,
  String? jobClass,
  int? localNumber,
  String? qualifications,
  String? datePosted,
  String? jobDescription,
  String? jobTitle,
  String? perDiem,
  String? agreement,
  String? numberOfJobs,
  DateTime? timestamp,
  String? startDate,
  String? startTime,
}) {
  final firestoreData = mapToFirestore(
    <String, dynamic>{
      'local': local,
      'classification': classification,
      'company': company,
      'location': location,
      'hours': hours,
      'wage': wage,
      'sub': sub,
      'jobClass': jobClass,
      'localNumber': localNumber,
      'qualifications': qualifications,
      'date_posted': datePosted,
      'job_description': jobDescription,
      'job_title': jobTitle,
      'per_diem': perDiem,
      'agreement': agreement,
      'numberOfJobs': numberOfJobs,
      'timestamp': timestamp,
      'startDate': startDate,
      'startTime': startTime,
    }.withoutNulls,
  );

  return firestoreData;
}

class JobsRecordDocumentEquality implements Equality<JobsRecord> {
  const JobsRecordDocumentEquality();

  @override
  bool equals(JobsRecord? e1, JobsRecord? e2) {
    const listEquality = ListEquality();
    return e1?.local == e2?.local &&
        e1?.classification == e2?.classification &&
        e1?.company == e2?.company &&
        e1?.location == e2?.location &&
        e1?.hours == e2?.hours &&
        e1?.wage == e2?.wage &&
        e1?.sub == e2?.sub &&
        e1?.jobClass == e2?.jobClass &&
        e1?.localNumber == e2?.localNumber &&
        e1?.qualifications == e2?.qualifications &&
        e1?.datePosted == e2?.datePosted &&
        e1?.jobDescription == e2?.jobDescription &&
        e1?.jobTitle == e2?.jobTitle &&
        e1?.perDiem == e2?.perDiem &&
        e1?.agreement == e2?.agreement &&
        e1?.numberOfJobs == e2?.numberOfJobs &&
        e1?.timestamp == e2?.timestamp &&
        e1?.startDate == e2?.startDate &&
        e1?.startTime == e2?.startTime &&
        listEquality.equals(e1?.booksYourOn, e2?.booksYourOn);
  }

  @override
  int hash(JobsRecord? e) => const ListEquality().hash([
        e?.local,
        e?.classification,
        e?.company,
        e?.location,
        e?.hours,
        e?.wage,
        e?.sub,
        e?.jobClass,
        e?.localNumber,
        e?.qualifications,
        e?.datePosted,
        e?.jobDescription,
        e?.jobTitle,
        e?.perDiem,
        e?.agreement,
        e?.numberOfJobs,
        e?.timestamp,
        e?.startDate,
        e?.startTime,
        e?.booksYourOn
      ]);

  @override
  bool isValidKey(Object? o) => o is JobsRecord;
}
