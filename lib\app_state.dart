import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class FFAppState extends ChangeNotifier {
  static FFAppState _instance = FFAppState._internal();

  factory FFAppState() {
    return _instance;
  }

  FFAppState._internal();

  static void reset() {
    _instance = FFAppState._internal();
  }

  Future initializePersistedState() async {
    prefs = await SharedPreferences.getInstance();
    _safeInit(() {
      _displayName = prefs.getString('ff_displayName') ?? _displayName;
    });
    _safeInit(() {
      _HomeLocal = prefs.getString('ff_HomeLocal') ?? _HomeLocal;
    });
    _safeInit(() {
      _isWorking = prefs.getBool('ff_isWorking') ?? _isWorking;
    });
    _safeInit(() {
      _FirstChoice = prefs.getInt('ff_FirstChoice') ?? _FirstChoice;
    });
    _safeInit(() {
      _SecondChoice = prefs.getInt('ff_SecondChoice') ?? _SecondChoice;
    });
    _safeInit(() {
      _ThirdChoice = prefs.getInt('ff_ThirdChoice') ?? _ThirdChoice;
    });
    _safeInit(() {
      _onboardingStatus0 =
          prefs.getBool('ff_onboardingStatus0') ?? _onboardingStatus0;
    });
    _safeInit(() {
      _onboardingStatus1 =
          prefs.getBool('ff_onboardingStatus1') ?? _onboardingStatus1;
    });
    _safeInit(() {
      _onboardingStatus2 =
          prefs.getBool('ff_onboardingStatus2') ?? _onboardingStatus2;
    });
    _safeInit(() {
      _onboardingStatus3 =
          prefs.getBool('ff_onboardingStatus3') ?? _onboardingStatus3;
    });
    _safeInit(() {
      _isProfileCompleted =
          prefs.getBool('ff_isProfileCompleted') ?? _isProfileCompleted;
    });
    _safeInit(() {
      _onBoardingStatus =
          prefs.getInt('ff_onBoardingStatus') ?? _onBoardingStatus;
    });
  }

  void update(VoidCallback callback) {
    callback();
    notifyListeners();
  }

  late SharedPreferences prefs;

  String _displayName = '';
  String get displayName => _displayName;
  set displayName(String value) {
    _displayName = value;
    prefs.setString('ff_displayName', value);
  }

  String _HomeLocal = '';
  String get HomeLocal => _HomeLocal;
  set HomeLocal(String value) {
    _HomeLocal = value;
    prefs.setString('ff_HomeLocal', value);
  }

  bool _isWorking = false;
  bool get isWorking => _isWorking;
  set isWorking(bool value) {
    _isWorking = value;
    prefs.setBool('ff_isWorking', value);
  }

  int _FirstChoice = 0;
  int get FirstChoice => _FirstChoice;
  set FirstChoice(int value) {
    _FirstChoice = value;
    prefs.setInt('ff_FirstChoice', value);
  }

  int _SecondChoice = 0;
  int get SecondChoice => _SecondChoice;
  set SecondChoice(int value) {
    _SecondChoice = value;
    prefs.setInt('ff_SecondChoice', value);
  }

  int _ThirdChoice = 0;
  int get ThirdChoice => _ThirdChoice;
  set ThirdChoice(int value) {
    _ThirdChoice = value;
    prefs.setInt('ff_ThirdChoice', value);
  }

  bool _onboardingStatus0 = false;
  bool get onboardingStatus0 => _onboardingStatus0;
  set onboardingStatus0(bool value) {
    _onboardingStatus0 = value;
    prefs.setBool('ff_onboardingStatus0', value);
  }

  bool _onboardingStatus1 = false;
  bool get onboardingStatus1 => _onboardingStatus1;
  set onboardingStatus1(bool value) {
    _onboardingStatus1 = value;
    prefs.setBool('ff_onboardingStatus1', value);
  }

  bool _onboardingStatus2 = false;
  bool get onboardingStatus2 => _onboardingStatus2;
  set onboardingStatus2(bool value) {
    _onboardingStatus2 = value;
    prefs.setBool('ff_onboardingStatus2', value);
  }

  bool _onboardingStatus3 = false;
  bool get onboardingStatus3 => _onboardingStatus3;
  set onboardingStatus3(bool value) {
    _onboardingStatus3 = value;
    prefs.setBool('ff_onboardingStatus3', value);
  }

  String _localNumber = '';
  String get localNumber => _localNumber;
  set localNumber(String value) {
    _localNumber = value;
  }

  bool _isProfileCompleted = false;
  bool get isProfileCompleted => _isProfileCompleted;
  set isProfileCompleted(bool value) {
    _isProfileCompleted = value;
    prefs.setBool('ff_isProfileCompleted', value);
  }

  int _onBoardingStatus = 0;
  int get onBoardingStatus => _onBoardingStatus;
  set onBoardingStatus(int value) {
    _onBoardingStatus = value;
    prefs.setInt('ff_onBoardingStatus', value);
  }
}

void _safeInit(Function() initializeField) {
  try {
    initializeField();
  } catch (_) {}
}

