name: journeyman_jobs
description: >
  Journeyman Jobs helps out-of-work Journeyman Lineman in the IBEW navigate a 
  confusing and outdated job referral process not only to get them back to 
  work quickly, but get them back to work with a potentially higher paying 
  job. 

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.0.0+1

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter
  auto_size_text: 3.0.0
  badges: ^3.1.2
  cached_network_image: 3.4.1
  cached_network_image_platform_interface: 4.1.1
  cached_network_image_web: 1.3.1
  cloud_firestore: ^5.6.7
  cloud_firestore_platform_interface: ^6.6.7
  cloud_firestore_web: ^4.4.7
  collection: 1.19.1
  dropdown_button2: 2.3.9
  firebase_auth: ^5.5.3
  firebase_auth_platform_interface: ^7.6.2
  firebase_auth_web: ^5.14.2
  firebase_core: ^3.13.0
  firebase_core_platform_interface: ^5.4.0
  firebase_core_web: ^2.22.0
  firebase_performance: ^0.10.1+5
  firebase_performance_platform_interface: ^0.1.5+5
  firebase_performance_web: ^0.1.7+11
  flutter_animate: ^4.5.2
  flutter_cache_manager: 3.4.1
  font_awesome_flutter: ^10.8.0
  from_css_color: 2.0.0
  go_router: ^15.1.2
  google_fonts: ^6.2.1
  google_generative_ai: ^0.4.7
  google_sign_in: ^6.3.0
  google_sign_in_android: ^6.2.1
  google_sign_in_ios: ^5.9.0
  google_sign_in_platform_interface: ^2.5.0
  google_sign_in_web: ^0.12.4+4
  intl: 0.19.0
  json_path: ^0.7.5
  mask_text_input_formatter: 2.9.0
  page_transition: ^2.2.1
  path_provider: ^2.1.5
  path_provider_android: ^2.2.17
  path_provider_foundation: ^2.4.1
  path_provider_linux: 2.2.1
  path_provider_platform_interface: 2.1.2
  path_provider_windows: 2.3.0
  percent_indicator: ^4.2.5
  plugin_platform_interface: 2.1.8
  provider: ^6.1.5
  rxdart: ^0.28.0
  shared_preferences: ^2.5.3
  shared_preferences_android: ^2.4.10
  shared_preferences_foundation: ^2.5.4
  shared_preferences_linux: 2.4.1
  shared_preferences_platform_interface: 2.4.1
  shared_preferences_web: ^2.4.3
  shared_preferences_windows: 2.4.1
  sign_in_with_apple: ^7.0.1
  sign_in_with_apple_platform_interface: ^2.0.0
  sign_in_with_apple_web: ^3.0.0
  smooth_page_indicator: ^1.2.1
  sqflite: ^2.4.2
  sqflite_common: ^2.5.5
  stream_transform: ^2.1.1
  timeago: ^3.7.1
  url_launcher: ^6.3.1
  url_launcher_android: ^6.3.16
  url_launcher_ios: ^6.3.3
  url_launcher_linux: ^3.2.1
  url_launcher_macos: ^3.2.2
  url_launcher_platform_interface: 2.3.2
  url_launcher_web: ^2.4.1
  url_launcher_windows: ^3.1.4

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.0

dependency_overrides:
  http: 1.2.2
  uuid: ^4.0.0

dev_dependencies:
  flutter_launcher_icons: ^0.14.3
  flutter_lints: ^5.0.0
  image: ^4.5.4
  lints: ^5.1.1
  flutter_test:
    sdk: flutter


flutter_launcher_icons:
  android: true
  ios: true
  remove_alpha_ios: true
  web:
    generate: true
  image_path: 'assets/images/app_launcher_icon.png'


# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/fonts/
    - assets/images/
    - assets/videos/
    - assets/audios/
    - assets/rive_animations/
    - assets/pdfs/
    - assets/jsons/

  fonts:
    - family: MyFlutterApp
      fonts:
        - asset: assets/fonts/flutter_icons_2.ttf


  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

